# PayTo AJAX Status Function Usage

## Overview

The `get_payto_agreement_payment_initiation_status()` AJAX function allows you to retrieve the current status of PayTo mandates and payment initiations for a specific order.

## AJAX Endpoint

**Action:** `get_payto_agreement_payment_initiation_status`
**Method:** POST
**URL:** `wp-admin/admin-ajax.php`

## Required Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `action` | string | Yes | Must be `get_payto_agreement_payment_initiation_status` |
| `nonce` | string | Yes | Security nonce (use `monoova_payto_status_nonce`) |
| `order_id` | integer | Yes | WooCommerce order ID |
| `order_key` | string | No | Order key for guest orders (if user not logged in) |

## Response Format

### Success Response
```json
{
    "success": true,
    "data": {
        "mandate_status": "Active",
        "payment_agreement_uid": "WC-PAYTO-123-1234567890",
        "payment_initiation_status": "ACSC",
        "payment_initiation_uid": "PI-123456789",
        "order_status": "processing",
        "order_id": 123
    }
}
```

### Error Response
```json
{
    "success": false,
    "data": {
        "message": "Error description"
    }
}
```

## Status Values

### Mandate Status
- `Created` - Agreement created, awaiting customer authorization
- `Active` - Agreement authorized and active
- `Paused` - Agreement temporarily paused
- `Cancelled` - Agreement cancelled
- `Rejected` - Agreement rejected by customer
- `Expired` - Agreement expired
- `null` - No mandate found

### Payment Initiation Status
- `ACCP` - Accepted Customer Profile
- `ACSP` - Accepted Settlement in Process
- `INPR` - In Process
- `RECV` - Received
- `SAFD` - Participant Down
- `SENT` - Sent To Payer
- `UNDV` - Undelivered
- `ACSC` - Accepted Settlement Completed
- `RJCT` - Rejected
- `null` - No payment initiation found

## JavaScript Usage Examples

### Basic Usage
```javascript
function checkPayToStatus(orderId, orderKey = '') {
    jQuery.ajax({
        url: monoova_payto_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'get_payto_agreement_payment_initiation_status',
            nonce: monoova_payto_ajax.status_nonce,
            order_id: orderId,
            order_key: orderKey
        },
        success: function(response) {
            if (response.success) {
                console.log('PayTo Status:', response.data);
                
                // Handle mandate status
                if (response.data.mandate_status) {
                    console.log('Mandate Status:', response.data.mandate_status);
                }
                
                // Handle payment status
                if (response.data.payment_initiation_status) {
                    console.log('Payment Status:', response.data.payment_initiation_status);
                }
            } else {
                console.error('Error:', response.data.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
        }
    });
}

// Usage
checkPayToStatus(123); // For logged-in users
checkPayToStatus(123, 'wc_order_key_here'); // For guest orders
```

### Polling for Status Updates
```javascript
function pollPayToStatus(orderId, orderKey = '', interval = 5000) {
    const poll = setInterval(function() {
        jQuery.ajax({
            url: monoova_payto_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_payto_agreement_payment_initiation_status',
                nonce: monoova_payto_ajax.status_nonce,
                order_id: orderId,
                order_key: orderKey
            },
            success: function(response) {
                if (response.success) {
                    const data = response.data;
                    
                    // Check if payment is completed
                    if (data.payment_initiation_status === 'ACSC' || 
                        data.order_status === 'processing' || 
                        data.order_status === 'completed') {
                        
                        console.log('Payment completed!');
                        clearInterval(poll);
                        
                        // Redirect or update UI
                        window.location.reload();
                        return;
                    }
                    
                    // Check if mandate is rejected or expired
                    if (data.mandate_status === 'Rejected' || 
                        data.mandate_status === 'Expired' ||
                        data.payment_initiation_status === 'RJCT') {
                        
                        console.log('Payment failed or expired');
                        clearInterval(poll);
                        
                        // Show error message
                        alert('Payment was rejected or expired. Please try again.');
                        return;
                    }
                    
                    // Update status display
                    updateStatusDisplay(data);
                }
            },
            error: function() {
                console.error('Failed to check status');
            }
        });
    }, interval);
    
    return poll; // Return interval ID so it can be cleared
}

function updateStatusDisplay(data) {
    // Update mandate status
    if (data.mandate_status) {
        jQuery('#mandate-status').text(data.mandate_status);
    }
    
    // Update payment status
    if (data.payment_initiation_status) {
        jQuery('#payment-status').text(data.payment_initiation_status);
    }
}

// Start polling
const pollInterval = pollPayToStatus(123, '', 3000); // Poll every 3 seconds

// Stop polling when needed
// clearInterval(pollInterval);
```

### Promise-based Wrapper
```javascript
function getPayToStatus(orderId, orderKey = '') {
    return new Promise((resolve, reject) => {
        jQuery.ajax({
            url: monoova_payto_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_payto_agreement_payment_initiation_status',
                nonce: monoova_payto_ajax.status_nonce,
                order_id: orderId,
                order_key: orderKey
            },
            success: function(response) {
                if (response.success) {
                    resolve(response.data);
                } else {
                    reject(new Error(response.data.message));
                }
            },
            error: function(xhr, status, error) {
                reject(new Error(error));
            }
        });
    });
}

// Usage with async/await
async function handlePayToStatus(orderId) {
    try {
        const status = await getPayToStatus(orderId);
        console.log('Status received:', status);
        
        if (status.payment_initiation_status === 'ACSC') {
            console.log('Payment completed successfully!');
        }
    } catch (error) {
        console.error('Error getting status:', error.message);
    }
}
```

## Security Notes

1. **Nonce Verification**: Always include the proper nonce for security
2. **Permission Checks**: The function verifies user permissions:
   - Admin users can check any order
   - Logged-in users can only check their own orders
   - Guest users must provide the correct order key
3. **Order Validation**: Only PayTo orders can be checked

## Integration Tips

1. **Thank You Page**: Use this on the order received page to show real-time status
2. **My Account**: Allow customers to check status of their PayTo orders
3. **Admin Interface**: Provide real-time status updates in admin order pages
4. **Webhooks Backup**: Use this as a backup when webhooks might be delayed
5. **Mobile Apps**: Perfect for mobile app integrations via AJAX calls

## Error Handling

Always handle these potential errors:
- Invalid order ID
- Permission denied
- Order not found
- Not a PayTo order
- Invalid security token
- Network errors

The function provides clear error messages to help with debugging and user experience.
