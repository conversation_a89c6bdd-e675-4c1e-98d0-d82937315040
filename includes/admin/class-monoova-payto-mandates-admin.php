<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Monoova PayTo Mandates Admin Interface
 *
 * @class       Monoova_PayTo_Mandates_Admin
 * @version     1.0.0
 * @package     Monoova_Payments_For_WooCommerce/Admin
 * <AUTHOR>
 */
class Monoova_PayTo_Mandates_Admin {

    /**
     * Mandate manager instance
     *
     * @var Monoova_PayTo_Mandate_Manager
     */
    private $mandate_manager;

    /**
     * Constructor
     */
    public function __construct() {
        $this->mandate_manager = new Monoova_PayTo_Mandate_Manager();
        
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Handle admin actions
        add_action('admin_post_monoova_delete_mandate', array($this, 'handle_delete_mandate'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_submenu_page(
            'woocommerce',
            __('PayTo Mandates', 'monoova-payments-for-woocommerce'),
            __('PayTo Mandates', 'monoova-payments-for-woocommerce'),
            'manage_woocommerce',
            'monoova-payto-mandates',
            array($this, 'display_mandates_page')
        );
    }

    /**
     * Display mandates admin page
     */
    public function display_mandates_page() {
        // Handle search and pagination
        $search = sanitize_text_field($_GET['search'] ?? '');
        $paged = max(1, intval($_GET['paged'] ?? 1));
        $per_page = 20;
        $offset = ($paged - 1) * $per_page;

        // Get mandates
        $mandates = $this->get_mandates_for_admin($search, $per_page, $offset);
        $total_mandates = $this->get_mandates_count($search);
        $total_pages = ceil($total_mandates / $per_page);

        // Get statistics
        $stats = $this->mandate_manager->get_mandate_statistics();

        ?>
        <div class="wrap">
            <h1><?php _e('PayTo Mandates', 'monoova-payments-for-woocommerce'); ?></h1>

            <!-- Statistics -->
            <div class="monoova-stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0;">
                <div class="monoova-stat-card" style="background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;"><?php _e('Total Mandates', 'monoova-payments-for-woocommerce'); ?></h3>
                    <p style="font-size: 24px; font-weight: bold; margin: 0; color: #0073aa;"><?php echo esc_html($stats['total']); ?></p>
                </div>
                <div class="monoova-stat-card" style="background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;"><?php _e('Active', 'monoova-payments-for-woocommerce'); ?></h3>
                    <p style="font-size: 24px; font-weight: bold; margin: 0; color: #46b450;"><?php echo esc_html($stats['active']); ?></p>
                </div>
                <div class="monoova-stat-card" style="background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;"><?php _e('Pending', 'monoova-payments-for-woocommerce'); ?></h3>
                    <p style="font-size: 24px; font-weight: bold; margin: 0; color: #ffb900;"><?php echo esc_html($stats['pending']); ?></p>
                </div>
                <div class="monoova-stat-card" style="background: #fff; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                    <h3 style="margin: 0 0 10px 0; color: #333;"><?php _e('Expired/Cancelled', 'monoova-payments-for-woocommerce'); ?></h3>
                    <p style="font-size: 24px; font-weight: bold; margin: 0; color: #dc3232;"><?php echo esc_html($stats['expired'] + $stats['cancelled']); ?></p>
                </div>
            </div>

            <!-- Search Form -->
            <form method="get" style="margin: 20px 0;">
                <input type="hidden" name="page" value="monoova-payto-mandates" />
                <p class="search-box">
                    <input type="search" name="search" value="<?php echo esc_attr($search); ?>" placeholder="<?php _e('Search by email or agreement UID...', 'monoova-payments-for-woocommerce'); ?>" />
                    <input type="submit" class="button" value="<?php _e('Search', 'monoova-payments-for-woocommerce'); ?>" />
                    <?php if ($search): ?>
                        <a href="<?php echo admin_url('admin.php?page=monoova-payto-mandates'); ?>" class="button"><?php _e('Clear', 'monoova-payments-for-woocommerce'); ?></a>
                    <?php endif; ?>
                </p>
            </form>

            <!-- Mandates Table -->
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('ID', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Agreement UID', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Customer', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Payment Method', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Max Amount', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Status', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Created', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Expires', 'monoova-payments-for-woocommerce'); ?></th>
                        <th><?php _e('Actions', 'monoova-payments-for-woocommerce'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($mandates)): ?>
                        <tr>
                            <td colspan="9" style="text-align: center; padding: 20px;">
                                <?php _e('No mandates found.', 'monoova-payments-for-woocommerce'); ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($mandates as $mandate): ?>
                            <tr>
                                <td><?php echo esc_html($mandate->id); ?></td>
                                <td>
                                    <code style="font-size: 11px;"><?php echo esc_html($mandate->payment_agreement_uid); ?></code>
                                </td>
                                <td>
                                    <?php 
                                    $user = get_user_by('id', $mandate->user_id);
                                    if ($user) {
                                        echo esc_html($user->display_name) . '<br>';
                                    }
                                    echo '<small>' . esc_html($mandate->customer_email) . '</small>';
                                    ?>
                                </td>
                                <td>
                                    <?php
                                    $summary = $this->mandate_manager->get_mandate_summary($mandate);
                                    echo esc_html($summary['payment_method_display'] ?: 'N/A');
                                    ?>
                                </td>
                                <td><?php echo wc_price($mandate->maximum_amount); ?></td>
                                <td>
                                    <span class="mandate-status mandate-status-<?php echo esc_attr(strtolower($mandate->status)); ?>">
                                        <?php echo esc_html($mandate->status); ?>
                                    </span>
                                </td>
                                <td><?php echo esc_html(date_i18n(get_option('date_format'), strtotime($mandate->created_at))); ?></td>
                                <td>
                                    <?php 
                                    if ($mandate->expires_at) {
                                        echo esc_html(date_i18n(get_option('date_format'), strtotime($mandate->expires_at)));
                                    } else {
                                        echo '<em>' . __('Never', 'monoova-payments-for-woocommerce') . '</em>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <form method="post" action="<?php echo admin_url('admin-post.php'); ?>" style="display: inline;">
                                        <input type="hidden" name="action" value="monoova_delete_mandate" />
                                        <input type="hidden" name="mandate_id" value="<?php echo esc_attr($mandate->id); ?>" />
                                        <?php wp_nonce_field('monoova_delete_mandate_' . $mandate->id); ?>
                                        <input type="submit" class="button button-small" value="<?php _e('Delete', 'monoova-payments-for-woocommerce'); ?>" 
                                               onclick="return confirm('<?php _e('Are you sure you want to delete this mandate?', 'monoova-payments-for-woocommerce'); ?>');" />
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>

            <!-- Pagination -->
            <?php if ($total_pages > 1): ?>
                <div class="tablenav bottom">
                    <div class="tablenav-pages">
                        <?php
                        $page_links = paginate_links(array(
                            'base' => add_query_arg('paged', '%#%'),
                            'format' => '',
                            'prev_text' => __('&laquo;'),
                            'next_text' => __('&raquo;'),
                            'total' => $total_pages,
                            'current' => $paged,
                            'type' => 'plain'
                        ));
                        echo $page_links;
                        ?>
                    </div>
                </div>
            <?php endif; ?>

            <style>
                .mandate-status {
                    padding: 3px 8px;
                    border-radius: 3px;
                    font-size: 11px;
                    font-weight: bold;
                    text-transform: uppercase;
                }
                .mandate-status-active { background: #46b450; color: white; }
                .mandate-status-created { background: #ffb900; color: white; }
                .mandate-status-expired { background: #dc3232; color: white; }
                .mandate-status-cancelled { background: #dc3232; color: white; }
                .mandate-status-rejected { background: #dc3232; color: white; }
                .mandate-status-paused { background: #666; color: white; }
            </style>
        </div>
        <?php
    }

    /**
     * Get mandates for admin display
     */
    private function get_mandates_for_admin($search = '', $limit = 20, $offset = 0) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . Monoova_PayTo_Mandate_Manager::TABLE_NAME;
        
        $where_clause = '1=1';
        $where_values = array();
        
        if (!empty($search)) {
            $where_clause .= ' AND (customer_email LIKE %s OR payment_agreement_uid LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name WHERE $where_clause ORDER BY created_at DESC LIMIT %d OFFSET %d",
            array_merge($where_values, array($limit, $offset))
        );
        
        $mandates = $wpdb->get_results($sql);
        
        // Decode JSON fields
        foreach ($mandates as $mandate) {
            $mandate->payer_details = json_decode($mandate->payer_details, true);
            $mandate->payee_details = json_decode($mandate->payee_details, true);
            $mandate->payment_terms = json_decode($mandate->payment_terms, true);
        }
        
        return $mandates;
    }

    /**
     * Get total mandates count for pagination
     */
    private function get_mandates_count($search = '') {
        global $wpdb;
        
        $table_name = $wpdb->prefix . Monoova_PayTo_Mandate_Manager::TABLE_NAME;
        
        $where_clause = '1=1';
        $where_values = array();
        
        if (!empty($search)) {
            $where_clause .= ' AND (customer_email LIKE %s OR payment_agreement_uid LIKE %s)';
            $search_term = '%' . $wpdb->esc_like($search) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }
        
        if (!empty($where_values)) {
            $sql = $wpdb->prepare("SELECT COUNT(*) FROM $table_name WHERE $where_clause", $where_values);
        } else {
            $sql = "SELECT COUNT(*) FROM $table_name WHERE $where_clause";
        }
        
        return $wpdb->get_var($sql);
    }

    /**
     * Handle delete mandate action
     */
    public function handle_delete_mandate() {
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('You do not have permission to perform this action.', 'monoova-payments-for-woocommerce'));
        }

        $mandate_id = intval($_POST['mandate_id'] ?? 0);
        
        if (!$mandate_id || !wp_verify_nonce($_POST['_wpnonce'], 'monoova_delete_mandate_' . $mandate_id)) {
            wp_die(__('Invalid request.', 'monoova-payments-for-woocommerce'));
        }

        if ($this->mandate_manager->delete_mandate($mandate_id)) {
            $redirect_url = add_query_arg(
                array('page' => 'monoova-payto-mandates', 'deleted' => '1'),
                admin_url('admin.php')
            );
        } else {
            $redirect_url = add_query_arg(
                array('page' => 'monoova-payto-mandates', 'error' => '1'),
                admin_url('admin.php')
            );
        }

        wp_redirect($redirect_url);
        exit;
    }
}

// Initialize admin interface
if (is_admin()) {
    new Monoova_PayTo_Mandates_Admin();
}
