# PayTo Mandate Storage Solution

## Overview

This solution implements a streamlined PayTo mandate storage system for the Monoova WooCommerce plugin, enabling express checkout functionality by reusing stored payment agreements.

## Architecture

### 1. Database Storage (`Monoova_PayTo_Mandate_Manager`)

**Custom Table: `wp_monoova_payto_mandates`**

```sql
CREATE TABLE wp_monoova_payto_mandates (
    id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    payment_agreement_uid varchar(255) NOT NULL,
    user_id bigint(20) unsigned NOT NULL,
    customer_email varchar(100) NOT NULL,
    agreement_type varchar(10) NOT NULL DEFAULT 'VARI',
    maximum_amount decimal(10,2) NOT NULL DEFAULT 1000.00,
    status varchar(20) NOT NULL DEFAULT 'Created',
    payment_method varchar(20) NOT NULL DEFAULT 'PAYTO',
    automatic_renewal tinyint(1) NOT NULL DEFAULT 0,
    expires_at datetime DEFAULT NULL,
    created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY payment_agreement_uid (payment_agreement_uid),
    KEY user_id (user_id),
    KEY customer_email (customer_email),
    KEY status (status)
);
```

**Key Features:**
- Stores essential PayTo mandate information
- Links to WordPress users and customer emails
- Tracks mandate status and expiration
- Lightweight design - detailed agreement data loaded via API when needed
- Automatic table creation and versioning

### 2. Integration Points

#### A. PayTo Gateway Integration
- **Mandate Storage**: After successful agreement creation, mandate details are stored in the database
- **Express Checkout**: Checks for existing active mandates before creating new agreements
- **Status Updates**: Webhook events update mandate status in real-time

#### B. Webhook Integration
- **Agreement Status Updates**: Webhook handler updates mandate status when agreement status changes
- **Real-time Synchronization**: Ensures local mandate status matches Monoova API status



## Implementation Details

### 1. Mandate Storage Process

```php
// In process_payment() method
$mandate_data = array(
    'payment_agreement_uid' => $payment_agreement_uid,
    'user_id' => $customer_id ?: 0,
    'customer_email' => $billing_email,
    'agreement_type' => 'VARI',
    'maximum_amount' => $unified_settings['payto_max_amount'] ?? 1000,
    'status' => $agreement_status,
    'payment_method' => 'PAYTO',
    'automatic_renewal' => false,
    'expires_at' => isset($end_date_obj) ? $end_date_obj->getTimestamp() : null,
);

$mandate_id = $this->mandate_manager->store_mandate($mandate_data);
```

### 2. Express Checkout Flow

```php
// Check for existing mandate
$existing_mandate = $this->mandate_manager->get_active_mandate_for_user($customer_id, $billing_email);

if ($existing_mandate && $this->should_use_existing_mandate($order, $existing_mandate)) {
    // Get full agreement details from API
    $api_response = $this->api->get_payto_agreement($existing_mandate->payment_agreement_uid);

    // Use existing mandate for payment
    return $this->process_payment_with_existing_mandate($order, $existing_mandate);
}
```

### 3. Mandate Validation

```php
private function should_use_existing_mandate($order, $mandate) {
    // Check order amount vs mandate maximum
    $order_total = floatval($order->get_total());
    $mandate_max = floatval($mandate->maximum_amount);
    
    if ($order_total > $mandate_max) {
        return false;
    }
    
    // Check expiration
    if (!empty($mandate->expires_at)) {
        $expires_timestamp = strtotime($mandate->expires_at);
        if ($expires_timestamp && $expires_timestamp < time()) {
            return false;
        }
    }
    
    return true;
}
```

## API Integration

### 1. Create Payment Agreement
- **Endpoint**: `POST /au/payto/pam-v1/PaymentAgreement`
- **Response**: Contains `paymentAgreementUID` and `PaymentAgreementStatus`
- **Storage**: Mandate details stored immediately after successful creation

### 2. Get Payment Agreement
- **Endpoint**: `GET /au/payto/pam-v1/paymentAgreement/{paymentAgreementUID}`
- **Usage**: Verify mandate status before reuse
- **Validation**: Ensures mandate is still active before creating payment

### 3. Create Payment Instruction
- **Endpoint**: `POST /au/payto/pas-v1/paymentInstruction`
- **Usage**: Initiate payment using existing mandate
- **Express Checkout**: Direct payment creation without new agreement

## Webhook Handling

### 1. Payment Agreement Notifications
```php
// Webhook events handled:
- PaymentAgreementCreated
- PaymentAgreementActive
- PaymentAgreementCancelled
- PaymentAgreementExpired
- PaymentAgreementRejected

// Status update in database
$mandate_manager->update_mandate_status($payment_agreement_uid, $agreement_status);
```

### 2. Payment Instruction Notifications
- Payment status updates tracked in order meta
- Subscription payments handled for recurring scenarios

## User Experience

### 1. Checkout Flow
1. **First Time**: Customer enters PayTo details, agreement created and stored
2. **Return Customer**: Express checkout option displayed if active mandate exists
3. **Express Checkout**: Customer can use existing mandate or create new one
4. **Validation**: System validates mandate suitability (amount, expiration)

### 2. Lightweight Design
- **Minimal Storage**: Only essential mandate information stored locally
- **API Integration**: Full agreement details loaded from Monoova API when needed
- **Efficient Queries**: Optimized database structure for fast lookups
- **Real-time Data**: Always uses current agreement status from API

## Security Considerations

### 1. Data Protection
- Sensitive payment details masked in admin interface
- JSON storage for flexible data structure
- Proper sanitization and validation

### 2. Access Control
- Admin interface restricted to `manage_woocommerce` capability
- Nonce verification for admin actions
- User-specific mandate access

## Maintenance

### 1. Automated Cleanup
```php
// Daily cron job to expire old mandates
wp_schedule_event(time(), 'daily', 'monoova_cleanup_expired_mandates');
```

### 2. Database Versioning
- Automatic table creation and updates
- Version tracking for future migrations
- Backward compatibility considerations

## Configuration

### 1. Settings Integration
- Maximum amount configurable per mandate
- Expiration settings
- Express checkout enablement

### 2. Filters and Hooks
```php
// Customizable agreement UID generation
$agreement_uid = apply_filters('monoova_payto_agreement_uid', $default_uid, $order);

// Mandate validation customization
$is_valid = apply_filters('monoova_payto_mandate_valid', $is_valid, $mandate, $order);
```

## Benefits

1. **Express Checkout**: Faster payment processing for returning customers
2. **Reduced Friction**: No need to re-enter payment details
3. **Better UX**: Seamless payment experience
4. **Lightweight Storage**: Minimal database footprint with API-driven data
5. **Scalability**: Efficient database design for high-volume stores
6. **Real-time Accuracy**: Always uses current mandate status from Monoova API
7. **Compliance**: Proper mandate lifecycle management

## Future Enhancements

1. **Multi-Currency Support**: Handle different currencies per mandate
2. **Mandate Templates**: Pre-configured mandate types
3. **Customer Dashboard**: Let customers manage their own mandates
4. **Analytics**: Detailed reporting on mandate usage
5. **Admin Interface**: Optional admin page for mandate management
6. **API Endpoints**: REST API for third-party integrations
