<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Monoova PayTo Mandate Manager
 * 
 * Handles storage and retrieval of PayTo mandates for express checkout and reuse
 *
 * @class       Monoova_PayTo_Mandate_Manager
 * @version     1.0.0
 * @package     Monoova_Payments_For_WooCommerce/Classes
 * <AUTHOR>
 */
class Monoova_PayTo_Mandate_Manager {

    /**
     * Table name for storing PayTo mandates
     */
    const TABLE_NAME = 'monoova_payto_mandates';

    /**
     * Database version for migrations
     */
    const DB_VERSION = '1.0.0';

    /**
     * Logger instance
     * @var WC_Logger|null
     */
    private $logger;

    /**
     * Constructor
     */
    public function __construct() {
        $this->logger = wc_get_logger();

        // Hook into WordPress init to create table if needed
        add_action('init', array($this, 'maybe_create_table'));

        // Schedule cleanup cron job
        add_action('monoova_cleanup_expired_mandates', array($this, 'cleanup_expired_mandates'));

        if (!wp_next_scheduled('monoova_cleanup_expired_mandates')) {
            wp_schedule_event(time(), 'daily', 'monoova_cleanup_expired_mandates');
        }
    }

    /**
     * Create the PayTo mandates table if it doesn't exist
     */
    public function maybe_create_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        $installed_version = get_option('monoova_payto_mandates_db_version', '0.0.0');
        
        if (version_compare($installed_version, self::DB_VERSION, '<')) {
            $this->create_table();
            update_option('monoova_payto_mandates_db_version', self::DB_VERSION);
        }
    }

    /**
     * Create the PayTo mandates table
     */
    private function create_table() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id bigint(20) unsigned NOT NULL AUTO_INCREMENT,
            payment_agreement_uid varchar(255) NOT NULL,
            user_id bigint(20) unsigned NOT NULL,
            customer_email varchar(100) NOT NULL,
            agreement_type varchar(10) NOT NULL DEFAULT 'VARI',
            maximum_amount decimal(10,2) NOT NULL DEFAULT 1000.00,
            status varchar(20) NOT NULL DEFAULT 'Created',
            payment_method varchar(20) NOT NULL DEFAULT 'PAYTO',
            automatic_renewal tinyint(1) NOT NULL DEFAULT 0,
            payer_details longtext,
            payee_details longtext,
            payment_terms longtext,
            expires_at datetime DEFAULT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY payment_agreement_uid (payment_agreement_uid),
            KEY user_id (user_id),
            KEY customer_email (customer_email),
            KEY status (status),
            KEY expires_at (expires_at),
            KEY created_at (created_at)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        $this->log('PayTo mandates table created or updated');
    }

    /**
     * Store a PayTo mandate
     *
     * @param array $mandate_data Mandate data from API response and order
     * @return int|false Mandate ID on success, false on failure
     */
    public function store_mandate($mandate_data) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        // Validate required fields
        $required_fields = ['payment_agreement_uid', 'user_id', 'customer_email'];
        foreach ($required_fields as $field) {
            if (empty($mandate_data[$field])) {
                $this->log("Missing required field: $field", 'error');
                return false;
            }
        }
        
        // Prepare data for insertion
        $data = array(
            'payment_agreement_uid' => sanitize_text_field($mandate_data['payment_agreement_uid']),
            'user_id' => absint($mandate_data['user_id']),
            'customer_email' => sanitize_email($mandate_data['customer_email']),
            'agreement_type' => sanitize_text_field($mandate_data['agreement_type'] ?? 'VARI'),
            'maximum_amount' => floatval($mandate_data['maximum_amount'] ?? 1000.00),
            'status' => sanitize_text_field($mandate_data['status'] ?? 'Created'),
            'payment_method' => sanitize_text_field($mandate_data['payment_method'] ?? 'PAYTO'),
            'automatic_renewal' => !empty($mandate_data['automatic_renewal']) ? 1 : 0,
            'payer_details' => wp_json_encode($mandate_data['payer_details'] ?? array()),
            'payee_details' => wp_json_encode($mandate_data['payee_details'] ?? array()),
            'payment_terms' => wp_json_encode($mandate_data['payment_terms'] ?? array()),
            'expires_at' => !empty($mandate_data['expires_at']) ? date('Y-m-d H:i:s', $mandate_data['expires_at']) : null,
        );
        
        // Check if mandate already exists
        $existing = $this->get_mandate_by_uid($mandate_data['payment_agreement_uid']);
        if ($existing) {
            // Update existing mandate
            $result = $wpdb->update(
                $table_name,
                $data,
                array('payment_agreement_uid' => $mandate_data['payment_agreement_uid']),
                array('%s', '%d', '%s', '%s', '%f', '%s', '%s', '%d', '%s', '%s', '%s', '%s'),
                array('%s')
            );
            
            if ($result !== false) {
                $this->log("Updated PayTo mandate: {$mandate_data['payment_agreement_uid']}");
                return $existing->id;
            }
        } else {
            // Insert new mandate
            $result = $wpdb->insert($table_name, $data, array('%s', '%d', '%s', '%s', '%f', '%s', '%s', '%d', '%s', '%s', '%s', '%s'));
            
            if ($result !== false) {
                $mandate_id = $wpdb->insert_id;
                $this->log("Stored new PayTo mandate: {$mandate_data['payment_agreement_uid']} (ID: $mandate_id)");
                return $mandate_id;
            }
        }
        
        $this->log("Failed to store PayTo mandate: {$mandate_data['payment_agreement_uid']}", 'error');
        return false;
    }

    /**
     * Get active PayTo mandate for a user
     *
     * @param int $user_id WordPress user ID
     * @param string $customer_email Customer email as fallback
     * @return object|null Mandate object or null if not found
     */
    public function get_active_mandate_for_user($user_id, $customer_email = '') {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        // Build query conditions
        $where_conditions = array();
        $where_values = array();
        
        if ($user_id > 0) {
            $where_conditions[] = 'user_id = %d';
            $where_values[] = $user_id;
        }
        
        if (!empty($customer_email)) {
            if (!empty($where_conditions)) {
                $where_conditions[] = 'OR customer_email = %s';
            } else {
                $where_conditions[] = 'customer_email = %s';
            }
            $where_values[] = $customer_email;
        }
        
        if (empty($where_conditions)) {
            return null;
        }
        
        $where_clause = '(' . implode(' ', $where_conditions) . ')';
        
        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name 
             WHERE $where_clause 
             AND status = 'Active' 
             AND (expires_at IS NULL OR expires_at > NOW())
             ORDER BY created_at DESC 
             LIMIT 1",
            ...$where_values
        );
        
        $mandate = $wpdb->get_row($sql);
        
        if ($mandate) {
            // Decode JSON fields
            $mandate->payer_details = json_decode($mandate->payer_details, true);
            $mandate->payee_details = json_decode($mandate->payee_details, true);
            $mandate->payment_terms = json_decode($mandate->payment_terms, true);
            
            $this->log("Found active mandate for user $user_id: {$mandate->payment_agreement_uid}");
        }
        
        return $mandate;
    }

    /**
     * Get mandate by payment agreement UID
     *
     * @param string $payment_agreement_uid
     * @return object|null Mandate object or null if not found
     */
    public function get_mandate_by_uid($payment_agreement_uid) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name WHERE payment_agreement_uid = %s",
            $payment_agreement_uid
        );
        
        $mandate = $wpdb->get_row($sql);
        
        if ($mandate) {
            // Decode JSON fields
            $mandate->payer_details = json_decode($mandate->payer_details, true);
            $mandate->payee_details = json_decode($mandate->payee_details, true);
            $mandate->payment_terms = json_decode($mandate->payment_terms, true);
        }
        
        return $mandate;
    }

    /**
     * Update mandate status
     *
     * @param string $payment_agreement_uid
     * @param string $status New status
     * @return bool Success
     */
    public function update_mandate_status($payment_agreement_uid, $status) {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        $result = $wpdb->update(
            $table_name,
            array('status' => sanitize_text_field($status)),
            array('payment_agreement_uid' => $payment_agreement_uid),
            array('%s'),
            array('%s')
        );
        
        if ($result !== false) {
            $this->log("Updated mandate status: $payment_agreement_uid -> $status");
            return true;
        }
        
        $this->log("Failed to update mandate status: $payment_agreement_uid", 'error');
        return false;
    }

    /**
     * Get all mandates for a user
     *
     * @param int $user_id WordPress user ID
     * @param string $status Optional status filter
     * @return array Array of mandate objects
     */
    public function get_user_mandates($user_id, $status = '') {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        $where_clause = 'user_id = %d';
        $where_values = array($user_id);
        
        if (!empty($status)) {
            $where_clause .= ' AND status = %s';
            $where_values[] = $status;
        }
        
        $sql = $wpdb->prepare(
            "SELECT * FROM $table_name WHERE $where_clause ORDER BY created_at DESC",
            ...$where_values
        );
        
        $mandates = $wpdb->get_results($sql);
        
        // Decode JSON fields for each mandate
        foreach ($mandates as $mandate) {
            $mandate->payer_details = json_decode($mandate->payer_details, true);
            $mandate->payee_details = json_decode($mandate->payee_details, true);
            $mandate->payment_terms = json_decode($mandate->payment_terms, true);
        }
        
        return $mandates;
    }

    /**
     * Clean up expired mandates
     *
     * @return int Number of cleaned up mandates
     */
    public function cleanup_expired_mandates() {
        global $wpdb;
        
        $table_name = $wpdb->prefix . self::TABLE_NAME;
        
        $result = $wpdb->query(
            "UPDATE $table_name 
             SET status = 'Expired' 
             WHERE expires_at IS NOT NULL 
             AND expires_at < NOW() 
             AND status NOT IN ('Expired', 'Cancelled', 'Rejected')"
        );
        
        if ($result > 0) {
            $this->log("Cleaned up $result expired PayTo mandates");
        }
        
        return $result;
    }

    /**
     * Get mandate summary for display (e.g., in checkout)
     *
     * @param object $mandate
     * @return array
     */
    public function get_mandate_summary($mandate) {
        if (!$mandate) {
            return array();
        }

        $payer_details = $mandate->payer_details ?: array();
        $payment_method_display = '';

        // Determine display text based on payer details
        if (!empty($payer_details['linkedPayId'])) {
            $payid_type = $payer_details['linkedPayIdType'] ?? '';
            switch ($payid_type) {
                case 'PhoneNumber':
                    $payment_method_display = 'Mobile: ' . $this->mask_sensitive_data($payer_details['linkedPayId']);
                    break;
                case 'Email':
                    $payment_method_display = 'Email: ' . $this->mask_sensitive_data($payer_details['linkedPayId']);
                    break;
                default:
                    $payment_method_display = 'PayID: ' . $this->mask_sensitive_data($payer_details['linkedPayId']);
            }
        } elseif (!empty($payer_details['linkedBsb']) && !empty($payer_details['linkedAccount'])) {
            $payment_method_display = 'Bank: ' . $payer_details['linkedBsb'] . ' / ' . $this->mask_sensitive_data($payer_details['linkedAccount']);
        }

        return array(
            'id' => $mandate->id,
            'payment_agreement_uid' => $mandate->payment_agreement_uid,
            'payment_method_display' => $payment_method_display,
            'maximum_amount' => $mandate->maximum_amount,
            'status' => $mandate->status,
            'created_at' => $mandate->created_at,
            'expires_at' => $mandate->expires_at,
        );
    }

    /**
     * Mask sensitive data for display
     *
     * @param string $data
     * @return string
     */
    private function mask_sensitive_data($data) {
        if (empty($data)) {
            return '';
        }

        $length = strlen($data);
        if ($length <= 4) {
            return str_repeat('*', $length);
        }

        // Show first 2 and last 2 characters
        return substr($data, 0, 2) . str_repeat('*', $length - 4) . substr($data, -2);
    }

    /**
     * Delete a mandate
     *
     * @param int $mandate_id
     * @return bool
     */
    public function delete_mandate($mandate_id) {
        global $wpdb;

        $table_name = $wpdb->prefix . self::TABLE_NAME;

        $result = $wpdb->delete(
            $table_name,
            array('id' => absint($mandate_id)),
            array('%d')
        );

        if ($result !== false) {
            $this->log("Deleted mandate ID: $mandate_id");
            return true;
        }

        $this->log("Failed to delete mandate ID: $mandate_id", 'error');
        return false;
    }

    /**
     * Get mandate statistics
     *
     * @return array
     */
    public function get_mandate_statistics() {
        global $wpdb;

        $table_name = $wpdb->prefix . self::TABLE_NAME;

        $stats = $wpdb->get_row(
            "SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) as active,
                SUM(CASE WHEN status = 'Created' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'Expired' THEN 1 ELSE 0 END) as expired,
                SUM(CASE WHEN status = 'Cancelled' THEN 1 ELSE 0 END) as cancelled
             FROM $table_name",
            ARRAY_A
        );

        return $stats ?: array(
            'total' => 0,
            'active' => 0,
            'pending' => 0,
            'expired' => 0,
            'cancelled' => 0
        );
    }

    /**
     * Log message
     *
     * @param string $message
     * @param string $level
     */
    private function log($message, $level = 'info') {
        if ($this->logger) {
            $this->logger->log($level, $message, array('source' => 'monoova-payto-mandate-manager'));
        }
    }
}
