<?php

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

/**
 * Monoova PayTo Payment Gateway.
 *
 * @class       Monoova_PayTo_Gateway
 * @extends     Monoova_Gateway
 * @version     1.0.2
 * @package     Monoova_Payments_For_WooCommerce/Classes/Payment
 * <AUTHOR>
 */
class Monoova_PayTo_Gateway extends Monoova_Gateway {
    /**
     * Gateway ID
     *
     * @var string
     */
    public $id = 'monoova_payto';

    /**
     * Gateway title
     *
     * @var string
     */
    public $method_title = 'PayTo';

    /**
     * Gateway description
     *
     * @var string
     */
    public $method_description = 'Accept payments via PayTo. Customers can approve a payment agreement to complete the purchase.';

    /**
     * Is test mode active?
     *
     * @var bool
     */
    public $testmode;

    /**
     * Is debug mode active?
     *
     * @var bool
     */
    public $debug;

    /**
     * Purpose code for PayTo agreements
     *
     * @var string
     */
    public $purpose;

    /**
     * Agreement expiry days
     *
     * @var string
     */
    public $agreement_expiry_days;

    /**
     * Payee type for PayTo agreements
     *
     * @var string
     */
    public $payee_type;

    /**
     * PayTo mandate manager instance
     *
     * @var Monoova_PayTo_Mandate_Manager
     */
    private $mandate_manager;

    /**
     * Constructor for the gateway.
     */
    public function __construct() {
        $this->id                 = 'monoova_payto';
        $this->icon               = apply_filters('woocommerce_monoova_payto_icon', MONOOVA_PLUGIN_URL . 'assets/images/payto-logo.svg');
        $this->has_fields         = true;
        $this->method_title       = __('PayTo', 'monoova-payments-for-woocommerce');
        $this->method_description = __('Accept payments via PayTo. Customers can approve a payment agreement to complete the purchase.', 'monoova-payments-for-woocommerce');

        parent::__construct();

        $this->init_form_fields();
        $this->init_settings();

        $this->title              = $this->get_option('title');
        $this->description        = $this->get_option('description');
        $this->enabled            = $this->get_option('enabled');
        $this->testmode           = 'yes' === $this->get_option('testmode');
        $this->debug              = 'yes' === $this->get_option('debug');
        $this->purpose            = $this->get_option('purpose', 'OTHR');
        $this->agreement_expiry_days = $this->get_option('agreement_expiry_days', '');
        $this->payee_type         = $this->get_option('payee_type', 'ORGN');

        // Initialize mandate manager
        $this->mandate_manager = new Monoova_PayTo_Mandate_Manager();

        add_action('woocommerce_update_options_payment_gateways_' . $this->id, array($this, 'process_admin_options'));
        add_action('wp_enqueue_scripts', array($this, 'payment_scripts'));

        // Add cron job for checking PayTo agreement status
        add_action('monoova_check_payto_agreements', array($this, 'check_pending_payto_agreements'));

        // Schedule cron job if not already scheduled
        if (!wp_next_scheduled('monoova_check_payto_agreements')) {
            wp_schedule_event(time(), 'hourly', 'monoova_check_payto_agreements');
        }

        // Register AJAX handlers
        add_action('wp_ajax_get_payto_agreement_payment_initiation_status', array($this, 'ajax_get_payto_agreement_payment_initiation_status'));
        add_action('wp_ajax_nopriv_get_payto_agreement_payment_initiation_status', array($this, 'ajax_get_payto_agreement_payment_initiation_status'));
    }

    /**
     * Check if gateway is available for use.
     *
     * @return bool
     */
    public function is_available() {
        // Basic availability check from parent
        if (!parent::is_available()) {
            return false;
        }

        // Check if unified gateway is controlling child gateways
        $unified_gateway = Monoova_Unified_Gateway::get_instance();
        if ($unified_gateway && $unified_gateway->is_controlling_child_gateways()) {
            // If unified gateway is active, check if this specific gateway is enabled through the unified interface
            return $unified_gateway->is_child_gateway_enabled('payto');
        }

        // If unified gateway is not controlling, use standard enabled check
        return $this->enabled === 'yes';
    }

    /**
     * Get the payment method icon.
     *
     * @return string
     */
    public function get_icon() {
        $icon = '<img src="' . esc_url(MONOOVA_PLUGIN_URL . 'assets/images/payto-logo.svg') . '" alt="Monoova PayTo Payment" style="height: 20px;" />';
        return apply_filters('woocommerce_gateway_icon', $icon, $this->id);
    }

    /**
     * Initialise Gateway Settings Form Fields.
     */
    public function init_form_fields() {
        $this->form_fields = array(
            'enabled' => array(
                'title'   => __('Enable/Disable', 'monoova-payments-for-woocommerce'),
                'type'    => 'checkbox',
                'label'   => __('Enable Monoova PayTo', 'monoova-payments-for-woocommerce'),
                'default' => 'no',
            ),
            'title' => array(
                'title'       => __('Title', 'monoova-payments-for-woocommerce'),
                'type'        => 'text',
                'description' => __('This controls the title which the user sees during checkout.', 'monoova-payments-for-woocommerce'),
                'default'     => __('PayTo', 'monoova-payments-for-woocommerce'),
                'desc_tip'    => true,
            ),
            'description' => array(
                'title'       => __('Description', 'monoova-payments-for-woocommerce'),
                'type'        => 'textarea',
                'description' => __('Payment method description that the customer will see on your checkout.', 'monoova-payments-for-woocommerce'),
                'default'     => __('Set up PayTo directly from your bank using BSB and Account Number or PayID.', 'monoova-payments-for-woocommerce'),
            ),
            'testmode' => array(
                'title'       => __('Test mode', 'monoova-payments-for-woocommerce'),
                'label'       => __('Enable Test Mode', 'monoova-payments-for-woocommerce'),
                'type'        => 'checkbox',
                'description' => __('Place the payment gateway in test mode using test API keys.', 'monoova-payments-for-woocommerce'),
                'default'     => 'yes',
                'desc_tip'    => true,
            ),
            'debug' => array(
                'title'       => __('Debug mode', 'monoova-payments-for-woocommerce'),
                'label'       => __('Enable Debug Mode', 'monoova-payments-for-woocommerce'),
                'type'        => 'checkbox',
                'description' => __('Enable debug logging for PayTo transactions. Check logs under WooCommerce > Status > Logs.', 'monoova-payments-for-woocommerce'),
                'default'     => 'yes',
                'desc_tip'    => true,
            ),
            'purpose' => array(
                'title'       => __('Purpose Code', 'monoova-payments-for-woocommerce'),
                'type'        => 'select',
                'description' => __('Purpose code for PayTo agreements as per ISO 20022 standards.', 'monoova-payments-for-woocommerce'),
                'default'     => 'OTHR',
                'desc_tip'    => true,
                'options'     => array(
                    'MORT' => __('MORT - Mortgage', 'monoova-payments-for-woocommerce'),
                    'UTIL' => __('UTIL - Utilities', 'monoova-payments-for-woocommerce'),
                    'LOAN' => __('LOAN - Loan', 'monoova-payments-for-woocommerce'),
                    'DEPD' => __('DEPD - Deposit', 'monoova-payments-for-woocommerce'),
                    'GAMP' => __('GAMP - Gaming/Gambling', 'monoova-payments-for-woocommerce'),
                    'RETL' => __('RETL - Retail', 'monoova-payments-for-woocommerce'),
                    'SALA' => __('SALA - Salary Payment', 'monoova-payments-for-woocommerce'),
                    'PERS' => __('PERS - Personal', 'monoova-payments-for-woocommerce'),
                    'GOVT' => __('GOVT - Government', 'monoova-payments-for-woocommerce'),
                    'PENS' => __('PENS - Pension', 'monoova-payments-for-woocommerce'),
                    'TAXS' => __('TAXS - Tax Payment', 'monoova-payments-for-woocommerce'),
                    'OTHR' => __('OTHR - Other', 'monoova-payments-for-woocommerce'),
                ),
            ),
            'agreement_expiry_days' => array(
                'title'       => __('Agreement Expiry (Days)', 'monoova-payments-for-woocommerce'),
                'type'        => 'number',
                'description' => __('Number of days from creation after which the PayTo agreement will expire. Leave empty for no expiry.', 'monoova-payments-for-woocommerce'),
                'default'     => '',
                'desc_tip'    => true,
                'custom_attributes' => array(
                    'min' => '1',
                    'max' => '365',
                    'step' => '1',
                ),
            ),
            'payee_type' => array(
                'title'       => __('Payee Type', 'monoova-payments-for-woocommerce'),
                'type'        => 'select',
                'description' => __('Type of payee for PayTo agreements. ORGN for organizations/businesses, PERS for individuals.', 'monoova-payments-for-woocommerce'),
                'default'     => 'ORGN',
                'desc_tip'    => true,
                'options'     => array(
                    'ORGN' => __('ORGN - Organization', 'monoova-payments-for-woocommerce'),
                    'PERS' => __('PERS - Person', 'monoova-payments-for-woocommerce'),
                ),
            ),
        );
    }

    /**
     * Output the payment form fields
     */
    public function payment_fields() {
        if ($this->description) {
            echo '<p>' . wp_kses_post($this->description) . '</p>';
        }

        echo '<div class="monoova-payto-form">';

        // Check for existing active mandate for express checkout
        $this->display_express_checkout_option();
        echo '<div class="payto-payment-method-selection">';
        echo '<p class="form-row form-row-wide">';
        echo '<label>' . esc_html__('Pay with', 'monoova-payments-for-woocommerce') . '</label>';
        echo '<input type="radio" id="payto_payment_method_payid" name="payto_payment_method" value="payid" checked>';
        echo '<label for="payto_payment_method_payid" style="margin-left: 10px; margin-right: 20px;">' . esc_html__('PayID', 'monoova-payments-for-woocommerce') . '</label>';
        echo '<input type="radio" id="payto_payment_method_bsb" name="payto_payment_method" value="bsb_account">';
        echo '<label for="payto_payment_method_bsb" style="margin-left: 10px;">' . esc_html__('BSB and account number', 'monoova-payments-for-woocommerce') . '</label>';
        echo '</p>';
        echo '</div>';

        // PayID Fields
        echo '<div id="payto_payid_fields" class="payto-fields-group">';
        echo '<h4>' . esc_html__('Enter your PayID details', 'monoova-payments-for-woocommerce') . '</h4>';

        // PayID Type Selection
        echo '<p class="form-row form-row-wide">';
        echo '<label for="payto_payid_type">' . esc_html__('PayID Type', 'monoova-payments-for-woocommerce') . ' <span class="required">*</span></label>';
        echo '<select id="payto_payid_type" name="payto_payid_type" class="select">';
        echo '<option value="PhoneNumber">' . esc_html__('Mobile Number', 'monoova-payments-for-woocommerce') . '</option>';
        echo '<option value="Email">' . esc_html__('Email Address', 'monoova-payments-for-woocommerce') . '</option>';
        echo '<option value="ABN">' . esc_html__('ABN (Australian Business Number)', 'monoova-payments-for-woocommerce') . '</option>';
        echo '<option value="ACN">' . esc_html__('ACN (Australian Company Number)', 'monoova-payments-for-woocommerce') . '</option>';
        echo '<option value="OrganisationId">' . esc_html__('Organisation ID', 'monoova-payments-for-woocommerce') . '</option>';
        echo '</select>';
        echo '</p>';

        // PayID Value Input
        echo '<p class="form-row form-row-wide">';
        echo '<label for="payto_payid_value" id="payto_payid_value_label">' . esc_html__('Mobile Number', 'monoova-payments-for-woocommerce') . ' <span class="required">*</span></label>';
        echo '<input type="tel" id="payto_payid_value" name="payto_payid_value" class="input-text" placeholder="********** or +***********" />';
        echo '<small class="form-text text-muted" id="payto_payid_help">' . esc_html__('Enter your mobile number with or without country code', 'monoova-payments-for-woocommerce') . '</small>';
        echo '</p>';
        echo '</div>';

        // BSB and Account Fields
        echo '<div id="payto_bsb_fields" class="payto-fields-group" style="display: none;">';
        echo '<h4>' . esc_html__('Enter your bank details', 'monoova-payments-for-woocommerce') . '</h4>';
        echo '<p class="form-row form-row-wide">';
        echo '<label for="payto_account_name">' . esc_html__('Name associated with bank account', 'monoova-payments-for-woocommerce') . ' <span class="required">*</span></label>';
        echo '<input type="text" id="payto_account_name" name="payto_account_name" class="input-text" placeholder="Enter your name" />';
        echo '</p>';
        echo '<p class="form-row form-row-first">';
        echo '<label for="payto_bsb">' . esc_html__('BSB', 'monoova-payments-for-woocommerce') . ' <span class="required">*</span></label>';
        echo '<input type="text" id="payto_bsb" name="payto_bsb" class="input-text" placeholder="Enter BSB" />';
        echo '</p>';
        echo '<p class="form-row form-row-last">';
        echo '<label for="payto_account_number">' . esc_html__('Account Number', 'monoova-payments-for-woocommerce') . ' <span class="required">*</span></label>';
        echo '<input type="text" id="payto_account_number" name="payto_account_number" class="input-text" placeholder="Enter your account number" />';
        echo '</p>';
        echo '<div class="clear"></div>';
        echo '</div>';

        echo '</div>';

        // Add JavaScript for form switching
        $this->add_payment_form_scripts();
    }

    /**
     * Add JavaScript for payment form interaction
     */
    private function add_payment_form_scripts() {
?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                function togglePayToFields() {
                    var selectedMethod = $('input[name="payto_payment_method"]:checked').val();
                    if (selectedMethod === 'payid') {
                        $('#payto_payid_fields').show();
                        $('#payto_bsb_fields').hide();
                    } else {
                        $('#payto_payid_fields').hide();
                        $('#payto_bsb_fields').show();
                    }
                }

                function updatePayIdField() {
                    var payidType = $('#payto_payid_type').val();
                    var $input = $('#payto_payid_value');
                    var $label = $('#payto_payid_value_label');
                    var $help = $('#payto_payid_help');

                    // Clear current value
                    $input.val('');

                    // Update field based on PayID type
                    switch (payidType) {
                        case 'PhoneNumber':
                            $label.html('<?php echo esc_js(__('Mobile Number', 'monoova-payments-for-woocommerce')); ?> <span class="required">*</span>');
                            $input.attr('type', 'tel');
                            $input.attr('placeholder', '********** or +***********');
                            $help.text('<?php echo esc_js(__('Enter your mobile number with or without country code', 'monoova-payments-for-woocommerce')); ?>');
                            break;
                        case 'Email':
                            $label.html('<?php echo esc_js(__('Email Address', 'monoova-payments-for-woocommerce')); ?> <span class="required">*</span>');
                            $input.attr('type', 'email');
                            $input.attr('placeholder', '<EMAIL>');
                            $help.text('<?php echo esc_js(__('Use the email address registered as your PayID', 'monoova-payments-for-woocommerce')); ?>');
                            break;
                        case 'ABN':
                            $label.html('<?php echo esc_js(__('ABN', 'monoova-payments-for-woocommerce')); ?> <span class="required">*</span>');
                            $input.attr('type', 'text');
                            $input.attr('placeholder', '***********');
                            $help.text('<?php echo esc_js(__('Enter your 11-digit ABN', 'monoova-payments-for-woocommerce')); ?>');
                            break;
                        case 'ACN':
                            $label.html('<?php echo esc_js(__('ACN', 'monoova-payments-for-woocommerce')); ?> <span class="required">*</span>');
                            $input.attr('type', 'text');
                            $input.attr('placeholder', '*********');
                            $help.text('<?php echo esc_js(__('Enter your 9-digit ACN', 'monoova-payments-for-woocommerce')); ?>');
                            break;
                        case 'OrganisationId':
                            $label.html('<?php echo esc_js(__('Organisation ID', 'monoova-payments-for-woocommerce')); ?> <span class="required">*</span>');
                            $input.attr('type', 'text');
                            $input.attr('placeholder', '<?php echo esc_js(__('Enter Organisation ID', 'monoova-payments-for-woocommerce')); ?>');
                            $help.text('<?php echo esc_js(__('Enter your organisation identifier', 'monoova-payments-for-woocommerce')); ?>');
                            break;
                    }
                }

                // Format mobile number input
                function formatMobileNumber(value) {
                    // Remove all non-digits
                    var numbers = value.replace(/\D/g, '');

                    // Handle different input patterns
                    if (numbers.indexOf('61') === 0) {
                        // Already has country code
                        return '+61' + numbers.slice(2);
                    } else if (numbers.indexOf('0') === 0) {
                        // Australian format starting with 0
                        return numbers;
                    } else if (numbers.length <= 9 && numbers.indexOf('0') !== 0) {
                        // Could be without leading 0, add it
                        return '0' + numbers;
                    }

                    return value;
                }

                // Initial setup
                togglePayToFields();
                updatePayIdField();

                // Event handlers
                $('input[name="payto_payment_method"]').change(function() {
                    togglePayToFields();
                });

                $('#payto_payid_type').change(function() {
                    updatePayIdField();
                });

                // Format mobile number on input (only for phone number type)
                $('#payto_payid_value').on('input', function() {
                    var payidType = $('#payto_payid_type').val();
                    if (payidType === 'PhoneNumber') {
                        var formatted = formatMobileNumber($(this).val());
                        $(this).val(formatted);
                    }
                });
            });
        </script>
        <style>
            .monoova-payto-form {
                padding: 15px;
                border: 1px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
                margin: 10px 0;
            }

            .payto-payment-method-selection label {
                display: inline;
                margin: 0;
                font-weight: normal;
            }

            .payto-fields-group {
                margin-top: 15px;
                padding: 10px;
                background-color: #fff;
                border-radius: 3px;
            }

            .payto-fields-group h4 {
                margin-top: 0;
                margin-bottom: 10px;
                color: #333;
            }

            .form-text.text-muted {
                color: #666;
                font-size: 12px;
                margin-top: 5px;
                display: block;
            }

            .payto-fields-group select {
                width: 100%;
                padding: 8px;
                border: 1px solid #ddd;
                border-radius: 3px;
                background-color: #fff;
            }
        </style>
<?php
    }

    /**
     * Validate payment form fields
     */
    public function validate_fields() {
        $payment_method = sanitize_text_field($_POST['payto_payment_method'] ?? '');

        if ($payment_method === 'payid') {
            $payid_type = sanitize_text_field($_POST['payto_payid_type'] ?? '');
            $payid_value = sanitize_text_field($_POST['payto_payid_value'] ?? '');

            if (empty($payid_type)) {
                wc_add_notice(__('PayID type is required.', 'monoova-payments-for-woocommerce'), 'error');
                return false;
            }

            if (empty($payid_value)) {
                wc_add_notice(__('PayID is required.', 'monoova-payments-for-woocommerce'), 'error');
                return false;
            }

            // Validate based on PayID type
            switch ($payid_type) {
                case 'PhoneNumber':
                    // Remove spaces and normalize for validation
                    $normalized_phone = preg_replace('/\s+/', '', $payid_value);
                    if (!preg_match('/^(\+61-?[4-5]\d{8}|0[4-5]\d{8}|\d{9})$/', $normalized_phone)) {
                        wc_add_notice(__('Please enter a valid Australian mobile number (e.g., 0422020901 or +***********).', 'monoova-payments-for-woocommerce'), 'error');
                        return false;
                    }
                    break;
                case 'Email':
                    if (!filter_var($payid_value, FILTER_VALIDATE_EMAIL)) {
                        wc_add_notice(__('Please enter a valid email address.', 'monoova-payments-for-woocommerce'), 'error');
                        return false;
                    }
                    break;
                case 'ABN':
                    $abn = preg_replace('/[^0-9]/', '', $payid_value);
                    if (!preg_match('/^\d{11}$/', $abn)) {
                        wc_add_notice(__('Please enter a valid 11-digit ABN.', 'monoova-payments-for-woocommerce'), 'error');
                        return false;
                    }
                    break;
                case 'ACN':
                    $acn = preg_replace('/[^0-9]/', '', $payid_value);
                    if (!preg_match('/^\d{9}$/', $acn)) {
                        wc_add_notice(__('Please enter a valid 9-digit ACN.', 'monoova-payments-for-woocommerce'), 'error');
                        return false;
                    }
                    break;
                case 'OrganisationId':
                    if (strlen(trim($payid_value)) < 2) {
                        wc_add_notice(__('Please enter a valid Organisation ID.', 'monoova-payments-for-woocommerce'), 'error');
                        return false;
                    }
                    break;
                default:
                    wc_add_notice(__('Invalid PayID type selected.', 'monoova-payments-for-woocommerce'), 'error');
                    return false;
            }
        } elseif ($payment_method === 'bsb_account') {
            $account_name = sanitize_text_field($_POST['payto_account_name'] ?? '');
            $bsb = sanitize_text_field($_POST['payto_bsb'] ?? '');
            $account_number = sanitize_text_field($_POST['payto_account_number'] ?? '');

            if (empty($account_name)) {
                wc_add_notice(__('Account name is required.', 'monoova-payments-for-woocommerce'), 'error');
                return false;
            }
            if (empty($bsb)) {
                wc_add_notice(__('BSB is required.', 'monoova-payments-for-woocommerce'), 'error');
                return false;
            }
            if (empty($account_number)) {
                wc_add_notice(__('Account number is required.', 'monoova-payments-for-woocommerce'), 'error');
                return false;
            }
            // Basic BSB validation (6 digits, can have dash)
            if (!preg_match('/^\d{3}-?\d{3}$/', $bsb)) {
                wc_add_notice(__('Please enter a valid BSB (6 digits).', 'monoova-payments-for-woocommerce'), 'error');
                return false;
            }
        } else {
            wc_add_notice(__('Please select a payment method.', 'monoova-payments-for-woocommerce'), 'error');
            return false;
        }

        return true;
    }
    /**
     * Process the payment and return the result.
     *
     * @param int $order_id
     * @return array
     */
    public function process_payment($order_id) {
        $order = wc_get_order($order_id);
        $this->log("Processing PayTo payment for order #{$order_id}.");
        $api = $this->get_api();
        if (!$api) {
            $this->log('Error: Monoova API client not initialized.', 'error');
            wc_add_notice(__('Payment gateway error. Please contact support.', 'monoova-payments-for-woocommerce'), 'error');
            return ['result' => 'failure'];
        }

        // Check if user wants to use existing mandate for express checkout
        $use_existing_mandate = !empty($_POST['payto_use_existing_mandate']);

        if ($use_existing_mandate) {
            $customer_id = $this->get_customer_id_for_order($order, true); // true = for mandate storage
            $billing_email = $order->get_billing_email();

            if ($customer_id > 0) {
                $existing_mandate = $this->mandate_manager->get_active_mandate_for_user($customer_id, $billing_email);

                if ($existing_mandate && $this->should_use_existing_mandate($order, $existing_mandate)) {
                    $this->log("Using existing mandate for express checkout: {$existing_mandate->payment_agreement_uid}");
                    return $this->process_payment_with_existing_mandate($order, $existing_mandate);
                } else {
                    $this->log("Existing mandate not suitable, creating new agreement");
                }
            } else {
                $this->log("Express checkout not available for guest users");
            }
        }

        $this->log("Creating new PayTo payment agreement for order #{$order_id}.");

        // Create new payment agreement
        $this->create_new_payment_agreement($order);
        return [
            'result'   => 'success',
            'redirect' => $this->get_return_url($order),
        ];
    }

    /**
     * Get customer payment details from POST data
     */
    private function get_customer_payment_details($payment_method) {
        if ($payment_method === 'payid') {
            $payid_type = sanitize_text_field($_POST['payto_payid_type'] ?? '');
            $payid_value = sanitize_text_field($_POST['payto_payid_value'] ?? '');

            if (empty($payid_type) || empty($payid_value)) {
                return false;
            }

            // Format PayID value based on type
            switch ($payid_type) {
                case 'PhoneNumber':
                    // Ensure mobile number is in correct format for API: +61-422020901
                    // Remove all non-numeric characters first
                    $cleaned_number = preg_replace('/[^0-9]/', '', $payid_value);

                    // Handle different input formats
                    if (strpos($cleaned_number, '61') === 0 && strlen($cleaned_number) === 11) {
                        // Already has 61 prefix, format as +61-XXXXXXXXX
                        $payid_value = '+61-' . substr($cleaned_number, 2);
                    } elseif (strpos($cleaned_number, '0') === 0 && strlen($cleaned_number) === 10) {
                        // Australian format starting with 0, convert to +61-XXXXXXXXX
                        $payid_value = '+61-' . substr($cleaned_number, 1);
                    } elseif (strlen($cleaned_number) === 9) {
                        // 9 digits without prefix, add +61-
                        $payid_value = '+61-' . $cleaned_number;
                    } else {
                        // Use as-is if already has + or other format
                        if (strpos($payid_value, '+') !== 0) {
                            // If no + sign, try to format as Australian number
                            $payid_value = '+61-' . ltrim($cleaned_number, '0');
                        } elseif (strpos($payid_value, '+61') === 0 && strpos($payid_value, '-') === false) {
                            // If already has +61 but no dash, add the dash
                            $payid_value = '+61-' . substr($payid_value, 3);
                        }
                    }
                    break;
                case 'ABN':
                    // Remove spaces and non-digits from ABN
                    $payid_value = preg_replace('/[^0-9]/', '', $payid_value);
                    break;
                case 'ACN':
                    // Remove spaces and non-digits from ACN
                    $payid_value = preg_replace('/[^0-9]/', '', $payid_value);
                    break;
                case 'Email':
                case 'OrganisationId':
                    // No formatting needed
                    break;
            }

            return [
                'method' => 'payid',
                'payid_type' => $payid_type,
                'payid' => $payid_value,
            ];
        } elseif ($payment_method === 'bsb_account') {
            $account_name = sanitize_text_field($_POST['payto_account_name'] ?? '');
            $bsb = sanitize_text_field($_POST['payto_bsb'] ?? '');
            $account_number = sanitize_text_field($_POST['payto_account_number'] ?? '');

            if (empty($account_name) || empty($bsb) || empty($account_number)) {
                return false;
            }

            // Format BSB (remove spaces and add dash if needed)
            $bsb = preg_replace('/[^0-9]/', '', $bsb);
            if (strlen($bsb) === 6) {
                $bsb = substr($bsb, 0, 3) . '-' . substr($bsb, 3);
            }

            return [
                'method' => 'bsb_account',
                'account_name' => $account_name,
                'bsb' => $bsb,
                'account_number' => $account_number,
            ];
        }

        return false;
    }

    /**
     * Output for the order received page.
     */
    public function thankyou_page($order_id) {
        $order = wc_get_order($order_id);
        if (!$order || $order->get_payment_method() !== $this->id) {
            return;
        }

        // Get PayTo agreement details from order meta
        $agreement_uid = $order->get_meta('_monoova_payto_agreement_uid', true);
        $agreement_reference = $order->get_meta('_monoova_payto_agreement_reference', true);
        $agreement_status = $order->get_meta('_monoova_payto_agreement_status', true);
        $authorization_url = $order->get_meta('_monoova_payto_authorization_url', true);
        $expires_at = (int) $order->get_meta('_monoova_payto_expires_at', true);

        if (empty($agreement_uid)) {
            $this->log("No PayTo agreement UID found for order #{$order_id}.", 'error');
            return;
        }

        $this->log("Displaying PayTo thank you page for order #{$order_id}. Agreement UID: {$agreement_uid}");

        // Prepare data for display
        $payto_data = [
            'agreement_uid' => $agreement_uid,
            'agreement_reference' => $agreement_reference,
            'agreement_status' => $agreement_status,
            'authorization_url' => $authorization_url,
            'order_total' => $order->get_formatted_order_total(),
            'order_number' => $order->get_order_number(),
            'expires_at' => $expires_at,
            'merchant_name' => get_bloginfo('name'),
        ];

        // Display PayTo instructions
        echo '<div class="monoova-payto-instructions">';
        echo '<h2>' . esc_html__('Complete Your PayTo Payment', 'monoova-payments-for-woocommerce') . '</h2>';
        echo '<p>' . esc_html__('A PayTo payment agreement has been created for your order. Follow the steps below to complete your payment:', 'monoova-payments-for-woocommerce') . '</p>';

        echo '<div class="payto-details">';
        echo '<h3>' . esc_html__('Payment Details', 'monoova-payments-for-woocommerce') . '</h3>';
        echo '<table class="payto-details-table">';
        echo '<tr><td><strong>' . esc_html__('Order Number:', 'monoova-payments-for-woocommerce') . '</strong></td><td>' . esc_html($payto_data['order_number']) . '</td></tr>';
        echo '<tr><td><strong>' . esc_html__('Amount:', 'monoova-payments-for-woocommerce') . '</strong></td><td>' . esc_html($payto_data['order_total']) . '</td></tr>';
        echo '<tr><td><strong>' . esc_html__('Agreement Reference:', 'monoova-payments-for-woocommerce') . '</strong></td><td>' . esc_html($payto_data['agreement_reference']) . '</td></tr>';
        echo '<tr><td><strong>' . esc_html__('Status:', 'monoova-payments-for-woocommerce') . '</strong></td><td>' . esc_html(ucfirst($payto_data['agreement_status'])) . '</td></tr>';

        if ($expires_at > 0) {
            $expiry_date = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $expires_at);
            echo '<tr><td><strong>' . esc_html__('Expires:', 'monoova-payments-for-woocommerce') . '</strong></td><td>' . esc_html($expiry_date) . '</td></tr>';
        }

        echo '</table>';
        echo '</div>';

        // Display authorization instructions
        echo '<div class="payto-instructions">';
        echo '<h3>' . esc_html__('How to Complete Payment', 'monoova-payments-for-woocommerce') . '</h3>';
        echo '<ol>';
        echo '<li>' . esc_html__('You will receive a notification on your mobile banking app from your bank.', 'monoova-payments-for-woocommerce') . '</li>';
        echo '<li>' . esc_html__('Open your banking app and look for PayTo payment requests.', 'monoova-payments-for-woocommerce') . '</li>';
        echo '<li>' . esc_html__('Review the payment details and authorize the payment agreement.', 'monoova-payments-for-woocommerce') . '</li>';
        echo '<li>' . esc_html__('Once authorized, the payment will be processed automatically.', 'monoova-payments-for-woocommerce') . '</li>';
        echo '</ol>';
        echo '</div>';

        // Display authorization URL if available (for testing or alternative flow)
        if (!empty($authorization_url)) {
            echo '<div class="payto-authorization-link">';
            echo '<h3>' . esc_html__('Alternative Authorization', 'monoova-payments-for-woocommerce') . '</h3>';
            echo '<p>' . esc_html__('If you did not receive a notification, you can also authorize the payment using the link below:', 'monoova-payments-for-woocommerce') . '</p>';
            echo '<a href="' . esc_url($authorization_url) . '" target="_blank" class="button">' . esc_html__('Authorize Payment', 'monoova-payments-for-woocommerce') . '</a>';
            echo '</div>';
        }

        echo '<div class="payto-help">';
        echo '<p><strong>' . esc_html__('Need Help?', 'monoova-payments-for-woocommerce') . '</strong></p>';
        echo '<p>' . esc_html__('If you have any issues with the PayTo payment, please contact us or check your banking app for PayTo payment requests.', 'monoova-payments-for-woocommerce') . '</p>';
        echo '</div>';

        echo '</div>';

        // Add some basic styling
        echo '<style>
        .monoova-payto-instructions {
            max-width: 600px;
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        .payto-details-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .payto-details-table td {
            padding: 8px 12px;
            border-bottom: 1px solid #eee;
        }
        .payto-instructions ol {
            margin: 15px 0;
            padding-left: 20px;
        }
        .payto-instructions li {
            margin: 8px 0;
        }
        .payto-authorization-link {
            margin: 20px 0;
            padding: 15px;
            background-color: #e7f3ff;
            border-radius: 5px;
        }
        .payto-help {
            margin-top: 20px;
            padding: 15px;
            background-color: #fff3cd;
            border-radius: 5px;
        }
        </style>';
    }

    /**
     * Add content to the WC emails.
     *
     * @param WC_Order $order
     * @param bool $sent_to_admin
     * @param bool $plain_text
     */
    public function email_instructions($order, $sent_to_admin, $plain_text = false) {
        if (!$order || $order->get_payment_method() !== $this->id) {
            return;
        }

        // Only show instructions for pending payments
        if (!in_array($order->get_status(), ['pending', 'on-hold'])) {
            return;
        }

        // Get PayTo agreement details
        $agreement_reference = $order->get_meta('_monoova_payto_agreement_reference', true);
        $agreement_status = $order->get_meta('_monoova_payto_agreement_status', true);
        $expires_at = (int) $order->get_meta('_monoova_payto_expires_at', true);

        if (empty($agreement_reference)) {
            return;
        }

        if ($plain_text) {
            echo "\n" . __('PayTo Payment Instructions', 'monoova-payments-for-woocommerce') . "\n";
            echo "=====================================\n\n";
            echo __('A PayTo payment agreement has been created for your order.', 'monoova-payments-for-woocommerce') . "\n\n";
            echo __('Agreement Reference:', 'monoova-payments-for-woocommerce') . " " . $agreement_reference . "\n";
            echo __('Amount:', 'monoova-payments-for-woocommerce') . " " . $order->get_formatted_order_total() . "\n";
            echo __('Status:', 'monoova-payments-for-woocommerce') . " " . ucfirst($agreement_status) . "\n";

            if ($expires_at > 0) {
                $expiry_date = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $expires_at);
                echo __('Expires:', 'monoova-payments-for-woocommerce') . " " . $expiry_date . "\n";
            }

            echo "\n" . __('To complete your payment:', 'monoova-payments-for-woocommerce') . "\n";
            echo "1. " . __('Check your mobile banking app for a PayTo payment request notification.', 'monoova-payments-for-woocommerce') . "\n";
            echo "2. " . __('Open your banking app and look for PayTo payment requests.', 'monoova-payments-for-woocommerce') . "\n";
            echo "3. " . __('Review the payment details and authorize the payment agreement.', 'monoova-payments-for-woocommerce') . "\n";
            echo "4. " . __('Once authorized, the payment will be processed automatically.', 'monoova-payments-for-woocommerce') . "\n\n";
            echo __('If you have any issues, please contact us for assistance.', 'monoova-payments-for-woocommerce') . "\n";
        } else {
            echo '<div style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">';
            echo '<h2 style="margin-top: 0;">' . esc_html__('PayTo Payment Instructions', 'monoova-payments-for-woocommerce') . '</h2>';
            echo '<p>' . esc_html__('A PayTo payment agreement has been created for your order.', 'monoova-payments-for-woocommerce') . '</p>';

            echo '<table style="width: 100%; border-collapse: collapse; margin: 15px 0;">';
            echo '<tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>' . esc_html__('Agreement Reference:', 'monoova-payments-for-woocommerce') . '</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">' . esc_html($agreement_reference) . '</td></tr>';
            echo '<tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>' . esc_html__('Amount:', 'monoova-payments-for-woocommerce') . '</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">' . esc_html($order->get_formatted_order_total()) . '</td></tr>';
            echo '<tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>' . esc_html__('Status:', 'monoova-payments-for-woocommerce') . '</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">' . esc_html(ucfirst($agreement_status)) . '</td></tr>';

            if ($expires_at > 0) {
                $expiry_date = date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $expires_at);
                echo '<tr><td style="padding: 8px; border-bottom: 1px solid #eee;"><strong>' . esc_html__('Expires:', 'monoova-payments-for-woocommerce') . '</strong></td><td style="padding: 8px; border-bottom: 1px solid #eee;">' . esc_html($expiry_date) . '</td></tr>';
            }

            echo '</table>';

            echo '<h3>' . esc_html__('To complete your payment:', 'monoova-payments-for-woocommerce') . '</h3>';
            echo '<ol style="margin: 15px 0; padding-left: 20px;">';
            echo '<li style="margin: 8px 0;">' . esc_html__('Check your mobile banking app for a PayTo payment request notification.', 'monoova-payments-for-woocommerce') . '</li>';
            echo '<li style="margin: 8px 0;">' . esc_html__('Open your banking app and look for PayTo payment requests.', 'monoova-payments-for-woocommerce') . '</li>';
            echo '<li style="margin: 8px 0;">' . esc_html__('Review the payment details and authorize the payment agreement.', 'monoova-payments-for-woocommerce') . '</li>';
            echo '<li style="margin: 8px 0;">' . esc_html__('Once authorized, the payment will be processed automatically.', 'monoova-payments-for-woocommerce') . '</li>';
            echo '</ol>';

            echo '<p style="margin-top: 20px;"><strong>' . esc_html__('Need help?', 'monoova-payments-for-woocommerce') . '</strong> ' . esc_html__('If you have any issues, please contact us for assistance.', 'monoova-payments-for-woocommerce') . '</p>';
            echo '</div>';
        }
    }

    /**
     * Load scripts.
     */
    public function payment_scripts() {
        // We need scripts on checkout/pay page.
        if (!is_checkout() && !isset($_GET['pay_for_order'])) {
            return;
        }

        if (!$this->is_available()) {
            return;
        }

        // Localize script with AJAX parameters for status checking
        wp_localize_script('jquery', 'monoova_payto_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'status_nonce' => wp_create_nonce('monoova_payto_status_nonce'),
        ));
    }

    /**
     * Create a payment initiation for an authorized PayTo agreement.
     *
     * @param int $order_id The order ID.
     * @param string $payment_agreement_uid The payment agreement UID.
     * @return array|WP_Error The payment response or error.
     */
    public function create_payment_initiation($order_id, $payment_agreement_uid = null) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return new WP_Error('invalid_order', __('Invalid order.', 'monoova-payments-for-woocommerce'));
        }

        // Get agreement UID from parameter or order meta
        if (empty($payment_agreement_uid)) {
            $payment_agreement_uid = $order->get_meta('_monoova_payto_agreement_uid', true);
        }

        if (empty($payment_agreement_uid)) {
            return new WP_Error('missing_agreement', __('Payment agreement UID not found.', 'monoova-payments-for-woocommerce'));
        }
        $api = $this->get_api();
        if (!$api) {
            return new WP_Error('api_error', __('API client not initialized.', 'monoova-payments-for-woocommerce'));
        }

        // Generate unique payment reference
        $payment_reference = apply_filters(
            'monoova_payto_payment_reference',
            'WC-PAYTO-PAYMENT_INIT-' . $order->get_id() . '-' . time(),
            $order
        );

        // Prepare payment initiation payload
        $payment_payload = [
            'paymentInitiationUID' => $payment_reference,
            'paymentAgreementUID' => $payment_agreement_uid,
            'paymentDetails' => [
                'amount' => $order->get_total(),
                'lodgementReference' => sprintf(
                    __('Payment for order #%s', 'monoova-payments-for-woocommerce'),
                    $order->get_order_number()
                ),
            ],
        ];

        $this->log("Creating PayTo payment initiation for order #{$order_id}. Payload: " . wp_json_encode($payment_payload), 'debug');

        // Create the payment initiation
        $response = $api->create_payto_payment($payment_payload);

        if (is_wp_error($response)) {
            $this->log("Error creating PayTo payment initiation for order #{$order_id}: " . $response->get_error_message(), 'error');
            return $response;
        }

        $this->log("PayTo payment initiation response for order #{$order_id}: " . wp_json_encode($response), 'debug');

        // Save payment initiation details
        $payment_initiation_uid = $response['paymentInitiationUID'] ?? '';
        $payment_status = $response['paymentInitiationStatus'] ?? '';

        if (!empty($payment_initiation_uid)) {
            $order->update_meta_data('_monoova_payto_payment_uid', $payment_initiation_uid);
        }

        if (!empty($payment_status)) {
            $order->update_meta_data('_monoova_payto_payment_status', $payment_status);
        }

        $order->update_meta_data('_monoova_payto_payment_reference', $payment_reference);
        $order->save();

        $this->log("PayTo payment initiation created successfully for order #{$order_id}. Payment UID: {$payment_initiation_uid}", 'info');

        return $response;
    }

    /**
     * Check PayTo agreement status and create payment if authorized.
     *
     * @param int $order_id The order ID.
     * @return bool True if payment was initiated, false otherwise.
     */
    public function check_and_process_authorized_agreement($order_id) {
        $order = wc_get_order($order_id);
        if (!$order) {
            return false;
        }

        $agreement_uid = $order->get_meta('_monoova_payto_agreement_uid', true);
        if (empty($agreement_uid)) {
            return false;
        }

        $api = $this->get_api();
        if (!$api) {
            $this->log("API client not initialized when checking agreement status for order #{$order_id}.", 'error');
            return false;
        }

        // Get current agreement status from API
        $response = $api->get_payto_agreement($agreement_uid);
        if (is_wp_error($response)) {
            $this->log("Error getting PayTo agreement status for order #{$order_id}: " . $response->get_error_message(), 'error');
            return false;
        }

        $agreement_status = $response['agreementStatus'] ?? '';
        $this->log("PayTo agreement status for order #{$order_id}: {$agreement_status}", 'debug');

        // Update the stored status
        $order->update_meta_data('_monoova_payto_agreement_status', $agreement_status);
        $order->save();

        // If agreement is authorized, create payment initiation
        if (strtolower($agreement_status) === 'authorized') {
            $payment_uid = $order->get_meta('_monoova_payto_payment_uid', true);

            // Only create payment if not already created
            if (empty($payment_uid)) {
                $result = $this->create_payment_initiation($order_id, $agreement_uid);

                if (!is_wp_error($result)) {
                    $order->add_order_note(__('PayTo agreement authorized. Payment initiation created.', 'monoova-payments-for-woocommerce'));
                    return true;
                } else {
                    $this->log("Failed to create payment initiation for authorized agreement (order #{$order_id}): " . $result->get_error_message(), 'error');
                    $order->add_order_note(__('PayTo agreement authorized but payment initiation failed. Manual intervention may be required.', 'monoova-payments-for-woocommerce'));
                }
            }
        }

        return false;
    }

    /**
     * Check pending PayTo agreements and update their status (cron job).
     * This method runs periodically to check for agreement status updates.
     */
    public function check_pending_payto_agreements() {
        $this->log('Running cron job to check pending PayTo agreements', 'info');

        if (!$this->get_api()) {
            $this->log('API client not initialized for PayTo agreement status check', 'error');
            return;
        }

        // Get orders with pending PayTo agreements (created in last 7 days)
        $orders = wc_get_orders([
            'status' => ['pending', 'on-hold'],
            'payment_method' => $this->id,
            'date_created' => '>' . (time() - 7 * DAY_IN_SECONDS),
            'meta_key' => '_monoova_payto_agreement_uid',
            'meta_compare' => 'EXISTS',
            'limit' => 50, // Process max 50 orders at a time
        ]);

        if (empty($orders)) {
            $this->log('No pending PayTo agreements found', 'debug');
            return;
        }

        $this->log('Found ' . count($orders) . ' pending PayTo agreements to check', 'info');

        $processed = 0;
        $updated = 0;

        foreach ($orders as $order) {
            $order_id = $order->get_id();
            $processed++;

            // Skip if payment has already been initiated
            $payment_uid = $order->get_meta('_monoova_payto_payment_uid', true);
            if (!empty($payment_uid)) {
                continue;
            }

            $result = $this->check_and_process_authorized_agreement($order_id);
            if ($result) {
                $updated++;
                $this->log("PayTo agreement processed for order #{$order_id}", 'info');
            }

            // Add a small delay to avoid overwhelming the API
            if ($processed % 10 === 0) {
                sleep(1);
            }
        }

        $this->log("PayTo agreement check completed. Processed: $processed, Updated: $updated", 'info');
    }

    /**
     * Check if we should use an existing mandate for this order
     *
     * @param WC_Order $order
     * @param object $mandate
     * @return bool
     */
    private function should_use_existing_mandate($order, $mandate) {
        // Check if order amount is within mandate maximum
        $order_total = floatval($order->get_total());
        $mandate_max = floatval($mandate->maximum_amount);

        if ($order_total > $mandate_max) {
            $this->log("Order total ($order_total) exceeds mandate maximum ($mandate_max)");
            return false;
        }

        // Check if mandate is still valid (not expired)
        if (!empty($mandate->expires_at)) {
            $expires_timestamp = strtotime($mandate->expires_at);
            if ($expires_timestamp && $expires_timestamp < time()) {
                $this->log("Mandate has expired: {$mandate->expires_at}");
                return false;
            }
        }

        // Additional validation can be added here (e.g., currency, merchant details)
        return true;
    }

    /**
     * Process payment using an existing active mandate
     *
     * @param WC_Order $order
     * @param object $mandate
     * @return array
     */
    private function process_payment_with_existing_mandate($order, $mandate) {
        $order_id = $order->get_id();
        $api = $this->get_api();
        try {
            // First, verify the mandate is still active with the API
            $api_response = $api->get_payto_agreement($mandate->payment_agreement_uid);

            if (is_wp_error($api_response)) {
                $this->log("Failed to verify mandate status: " . $api_response->get_error_message(), 'error');
                // Fall back to creating new mandate
                return $this->create_new_payment_agreement($order);
            }

            $api_status = strtolower($api_response['paymentAgreementDetails']['agreementStatus'] ?? '');

            if ($api_status !== 'active') {
                $this->log("Mandate is not active (status: $api_status), creating new agreement");
                // Update local status and create new mandate
                $this->mandate_manager->update_mandate_status($mandate->payment_agreement_uid, ucfirst($api_status));
                return $this->create_new_payment_agreement($order);
            }

            // Mandate is active, create payment initiation
            $this->log("Using existing active mandate for express checkout: {$mandate->payment_agreement_uid}");

            // Save mandate reference to order
            $order->update_meta_data('_monoova_payto_agreement_uid', $mandate->payment_agreement_uid);
            $order->update_meta_data('_monoova_payto_agreement_status', 'Active');
            $order->update_meta_data('_monoova_payto_mandate_id', $mandate->id);
            $order->update_meta_data('_monoova_payto_express_checkout', true);

            // Create payment initiation immediately
            $payment_result = $this->create_payment_initiation($order_id, $mandate->payment_agreement_uid);

            if (is_wp_error($payment_result)) {
                $this->log("Failed to create payment initiation: " . $payment_result->get_error_message(), 'error');
                throw new Exception(__('Could not initiate payment with existing mandate. Please try again.', 'monoova-payments-for-woocommerce'));
            }

            // Update order status
            $order->update_status(
                'pending',
                sprintf(
                    __('PayTo payment initiated using existing mandate. Agreement UID: %s. Awaiting payment completion.', 'monoova-payments-for-woocommerce'),
                    $mandate->payment_agreement_uid
                )
            );

            $order->save();

        } catch (Exception $e) {
            $this->log("Express checkout failed: " . $e->getMessage(), 'error');
            wc_add_notice($e->getMessage(), 'error');
            return ['result' => 'failure'];
        }
    }

    /**
     * Display express checkout option if user has active mandate
     * Only available for logged-in users
     */
    private function display_express_checkout_option() {
        // Express checkout only available for logged-in users
        if (!is_user_logged_in()) {
            return;
        }

        $current_user = wp_get_current_user();
        $existing_mandate = $this->mandate_manager->get_active_mandate_for_user($current_user->ID, $current_user->user_email);

        if (!$existing_mandate) {
            return;
        }

        $mandate_summary = $this->mandate_manager->get_mandate_summary($existing_mandate);

        echo '<div class="payto-express-checkout" style="margin-bottom: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #0073aa; border-radius: 5px;">';
        echo '<h4 style="margin-top: 0; color: #0073aa;">' . esc_html__('Express Checkout Available', 'monoova-payments-for-woocommerce') . '</h4>';
        echo '<p>' . esc_html__('You have an active PayTo mandate that can be used for faster checkout.', 'monoova-payments-for-woocommerce') . '</p>';

        echo '<div class="mandate-details" style="background: white; padding: 10px; border-radius: 3px; margin: 10px 0;">';
        echo '<strong>' . esc_html__('Agreement ID:', 'monoova-payments-for-woocommerce') . '</strong> ' . esc_html(substr($mandate_summary['payment_agreement_uid'], -8)) . '<br>';
        echo '<strong>' . esc_html__('Maximum Amount:', 'monoova-payments-for-woocommerce') . '</strong> ' . wc_price($mandate_summary['maximum_amount']) . '<br>';
        echo '<strong>' . esc_html__('Status:', 'monoova-payments-for-woocommerce') . '</strong> ' . esc_html($mandate_summary['status']);
        echo '</div>';

        echo '<p class="form-row form-row-wide">';
        echo '<input type="checkbox" id="payto_use_existing_mandate" name="payto_use_existing_mandate" value="1" checked>';
        echo '<label for="payto_use_existing_mandate" style="margin-left: 10px;">' . esc_html__('Use existing PayTo mandate for express checkout', 'monoova-payments-for-woocommerce') . '</label>';
        echo '</p>';
        echo '</div>';

        // Add JavaScript to hide/show payment method selection
        echo '<script type="text/javascript">
            jQuery(document).ready(function($) {
                function togglePaymentMethodSelection() {
                    var useExisting = $("#payto_use_existing_mandate").is(":checked");
                    if (useExisting) {
                        $(".payto-payment-method-selection, .payto-fields-group").hide();
                    } else {
                        $(".payto-payment-method-selection, .payto-fields-group").show();
                    }
                }

                $("#payto_use_existing_mandate").change(togglePaymentMethodSelection);
                togglePaymentMethodSelection();
            });
        </script>';
    }

    /**
     * Create a new payment agreement (extracted from process_payment for reuse)
     *
     * @param WC_Order $order
     * @return array
     */
    private function create_new_payment_agreement($order) {
        $order_id = $order->get_id();

        try {
            $unified_settings = $this->get_unified_gateway_settings();

            // Get customer payment details from checkout form
            $payment_method = sanitize_text_field($_POST['payto_payment_method'] ?? 'payid');
            $customer_payment_details = $this->get_customer_payment_details($payment_method);

            if (!$customer_payment_details) {
                wc_add_notice(__('Invalid payment details provided.', 'monoova-payments-for-woocommerce'), 'error');
                return ['result' => 'failure'];
            }

            // Get customer details
            $customer_id = $order->get_customer_id();
            $billing_email = $order->get_billing_email();

            // Generate unique agreement UID
            $agreement_uid = apply_filters(
                'monoova_payto_agreement_uid',
                'WC-PAYTO-' . $order->get_id() . '-' . time(),
                $order
            );

            // Get purpose code from settings (default to OTHR if not set)
            $purpose_code = $unified_settings['payto_purpose'] ?? 'OTHR';

            // Validate purpose code against API allowed values
            $allowed_purpose_codes = ['MORT', 'UTIL', 'LOAN', 'DEPD', 'GAMP', 'RETL', 'SALA', 'PERS', 'GOVT', 'PENS', 'TAXS', 'OTHR'];
            if (!in_array($purpose_code, $allowed_purpose_codes)) {
                $this->log("Invalid purpose code '{$purpose_code}'. Using default 'OTHR'.", 'warning');
                $purpose_code = 'OTHR';
            }

            // Calculate end date if configured
            $end_date = null;
            $end_date_obj = null;
            $expiry_days = (int) ($unified_settings['payto_agreement_expiry_days'] ?: 0);
            if ($expiry_days > 0) {
                $end_date_obj = new DateTime('now', new DateTimeZone('Australia/Sydney'));
                $end_date_obj->add(new DateInterval("P{$expiry_days}D"));
                $end_date = $end_date_obj->format('Y-m-d');
            }

            // Calculate respond by time (5 days default as per API)
            $respond_by_time = new DateTime('now', new DateTimeZone('UTC'));
            $respond_by_time->add(new DateInterval('P5D'));

            // Build payer details based on customer's payment method selection
            $api_customer_id = $this->get_customer_id_for_order($order, false); // false = for API usage (includes guest IDs)
            $payer_details = [
                'payerType' => 'PERS', // Person
                'payer' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
                'ultimatePayer' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name()),
                'payerPartyReference' => 'CUSTOMER-' . $api_customer_id,
            ];

            // Add customer's bank/PayID details to payer details
            if ($customer_payment_details['method'] === 'payid') {
                $payer_details['linkedPayId'] = $customer_payment_details['payid'];
                $payer_details['linkedPayIdType'] = $customer_payment_details['payid_type'];
            } else {
                $payer_details['linkedBsb'] = $customer_payment_details['bsb'];
                $payer_details['linkedAccount'] = $customer_payment_details['account_number'];
                $payer_details['payer'] = $customer_payment_details['account_name'];
                $payer_details['ultimatePayer'] = $customer_payment_details['account_name'];
            }

            // Prepare payment agreement payload according to Monoova PayTo API v1
            $agreement_payload = [
                'paymentAgreementUID' => $agreement_uid,
                'payeeDetails' => [
                    'payeeType' => $this->payee_type ?: 'ORGN',
                    'payeeLinkedBsb' => $unified_settings['static_bsb'] ?? '',
                    'payeeLinkedAccount' => $unified_settings['static_account_number'] ?? '',
                    'payeeAccountName' => $unified_settings['static_bank_account_name'] ?? get_bloginfo('name'),
                    'ultimatePayee' => get_bloginfo('name'),
                ],
                'payerDetails' => $payer_details,
                'paymentTerms' => [
                    'numberOfTransactionsPermitted' => 1,
                    'frequency' => 'ADHO', // Adhoc
                    'pointInTime' => null,
                    'amount' => null,
                    'maximumAmount' => $unified_settings['payto_max_amount'] ?? 1000,
                    'agreementType' => 'VARI', // Variable amount
                ],
                'paymentDetails' => [
                    'automaticRenewal' => true,
                    'description' => sprintf(
                        __('Payment for order #%s from %s', 'monoova-payments-for-woocommerce'),
                        $order->get_order_number(),
                        get_bloginfo('name')
                    ),
                    'shortDescription' => sprintf('Order #%s', $order->get_order_number()),
                    'purpose' => $purpose_code,
                    'respondByTime' => $respond_by_time->format('Y-m-d\TH:i:s\Z'),
                    'startDate' => date('Y-m-d'), // Today
                ],
            ];

            // Add end date if configured
            // if ($end_date) {
            //     $agreement_payload['paymentDetails']['endDate'] = $end_date;
            // }

            // Validate required account details
            if (
                empty($agreement_payload['payeeDetails']['payeeLinkedBsb']) ||
                empty($agreement_payload['payeeDetails']['payeeLinkedAccount'])
            ) {
                $this->log('Configuration error: Payee account details (BSB/Account Number) not configured.', 'error');
                throw new Exception(__('Payment gateway is not configured correctly. Please contact support.', 'monoova-payments-for-woocommerce'));
            }

            $this->log("PayTo agreement payload for order #{$order_id}: " . wp_json_encode($agreement_payload), 'debug');

            $api = $this->get_api();
            if (!$api) {
                $this->log('API client not initialized when creating PayTo agreement for order #{$order_id}.', 'error');
                throw new Exception(__('Payment gateway is not configured correctly. Please contact support.', 'monoova-payments-for-woocommerce'));
            }

            // Create the payment agreement
            $response = $api->create_payto_agreement($agreement_payload);

            if (is_wp_error($response)) {
                $error_msg = $response->get_error_message();
                $this->log("API error creating PayTo agreement for order #{$order_id}: " . $error_msg, 'error');

                // Log the specific error data if available
                $error_data = $response->get_error_data();
                if ($error_data) {
                    $this->log("PayTo API error details: " . wp_json_encode($error_data), 'error');
                }

                throw new Exception(__('Could not create payment agreement. Please try again or contact support.', 'monoova-payments-for-woocommerce'));
            }

            $this->log("PayTo agreement API response for order #{$order_id}: " . wp_json_encode($response), 'debug');

            // Extract agreement details from response
            $payment_agreement_uid = $response['paymentAgreementUID'] ?? '';
            $agreement_status = $response['PaymentAgreementStatus'] ?? '';

            if (empty($payment_agreement_uid)) {
                $this->log("Payment agreement UID not found in API response for order #{$order_id}.", 'error');
                throw new Exception(__('Could not retrieve payment agreement details. Please contact support.', 'monoova-payments-for-woocommerce'));
            }

            // Save agreement details to order meta
            $order->update_meta_data('_monoova_payto_agreement_uid', $payment_agreement_uid);
            $order->update_meta_data('_monoova_payto_agreement_reference', $agreement_uid);
            $order->update_meta_data('_monoova_payto_agreement_status', $agreement_status);
            $order->update_meta_data('_monoova_payto_customer_payment_method', $payment_method);
            $order->update_meta_data('_monoova_payto_customer_payment_details', $customer_payment_details);

            if (isset($end_date_obj)) {
                $order->update_meta_data('_monoova_payto_expires_at', $end_date_obj->getTimestamp());
            }

            // Store mandate in database for future reuse (only for logged-in users)
            // Guest users can still use PayTo but won't have express checkout capability
            $customer_id = $this->get_customer_id_for_order($order, true); // true = for mandate storage
            $billing_email = $order->get_billing_email();

            if ($customer_id > 0 && !empty($billing_email)) {
                $mandate_data = array(
                    'payment_agreement_uid' => $payment_agreement_uid,
                    'user_id' => $customer_id,
                    'customer_email' => $billing_email,
                    'agreement_type' => 'VARI',
                    'maximum_amount' => $unified_settings['payto_max_amount'] ?? 1000,
                    'status' => $agreement_status,
                    'payment_method' => 'PAYTO',
                    'automatic_renewal' => false,
                    'expires_at' => isset($end_date_obj) ? $end_date_obj->getTimestamp() : null,
                );

                $mandate_id = $this->mandate_manager->store_mandate($mandate_data);
                if ($mandate_id) {
                    $order->update_meta_data('_monoova_payto_mandate_id', $mandate_id);
                    $this->log("Stored PayTo mandate (ID: $mandate_id) for logged-in user on order #{$order_id}");
                } else {
                    $this->log("Failed to store PayTo mandate for order #{$order_id}", 'warning');
                }
            } else {
                $this->log("Skipping mandate storage for guest user on order #{$order_id} - express checkout not available for guests");
            }

            $order->save();

            $this->log("Successfully created PayTo agreement for order #{$order_id}. Agreement UID: {$payment_agreement_uid}", 'info');

        } catch (Exception $e) {
            $this->log("PayTo payment processing exception for order #{$order_id}: " . $e->getMessage(), 'error');
            wc_add_notice($e->getMessage(), 'error');
            return ['result' => 'failure'];
        }
    }

    /**
     * AJAX handler to get PayTo agreement and payment initiation status
     * It should use for API polling for payment status updates.
     */
    public function ajax_get_payto_agreement_payment_initiation_status() {
        // Verify nonce for security
        if (!wp_verify_nonce($_POST['nonce'] ?? '', 'monoova_payto_status_nonce')) {
            wp_send_json_error(array('message' => 'Invalid security token'));
            return;
        }

        // Get order ID from request
        $order_id = absint($_POST['order_id'] ?? 0);

        if (!$order_id) {
            wp_send_json_error(array('message' => 'Invalid order ID'));
            return;
        }

        // Get order
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_send_json_error(array('message' => 'Order not found'));
            return;
        }

        $response_data = array();

        // Get PayTo mandate status from order metadata (works for both guest and logged-in users)
        $payment_agreement_uid = $order->get_meta('_monoova_payto_agreement_uid', true);
        $mandate_status = $order->get_meta('_monoova_payto_agreement_status', true);

        $response_data['mandate_status'] = !empty($mandate_status) ? $mandate_status : null;
        $response_data['payment_agreement_uid'] = !empty($payment_agreement_uid) ? $payment_agreement_uid : null;

        // Get PayTo payment initiation status
        $payment_status = $order->get_meta('_monoova_payto_payment_status', true);
        $response_data['payment_initiation_status'] = !empty($payment_status) ? $payment_status : null;

        // Get payment UID if available
        $payment_uid = $order->get_meta('_monoova_payto_payment_uid', true);
        $response_data['payment_initiation_uid'] = !empty($payment_uid) ? $payment_uid : null;

        // Get order status for additional context
        $response_data['order_status'] = $order->get_status();
        $response_data['order_id'] = $order_id;

        // Log the status check
        $this->log("AJAX status check for order #{$order_id}: Mandate status: " . ($response_data['mandate_status'] ?: 'null') . ", Payment status: " . ($response_data['payment_initiation_status'] ?: 'null'));

        wp_send_json_success($response_data);
    }



    /**
     * Get customer ID for order with proper authentication and fallback handling
     *
     * @param WC_Order $order The order object
     * @param bool $for_mandate_storage Whether this is for mandate storage (returns 0 for guests) or API usage (returns guest ID)
     * @return int|string Customer ID for use in mandate storage (int for logged-in, 0 for guests) or guest string for API
     * @since 1.0.0
     */
    private function get_customer_id_for_order($order, $for_mandate_storage = true) {
        // First, try to get the customer ID from the order
        $order_customer_id = $order->get_customer_id();

        // If order has a valid customer ID (not 0), use it
        if (!empty($order_customer_id) && $order_customer_id > 0) {
            $this->log("Using order customer ID: {$order_customer_id} for order {$order->get_id()}");
            return $order_customer_id;
        }

        // Check if a user is currently logged in
        $current_user_id = get_current_user_id();
        if ($current_user_id > 0) {
            // Update the order with the current user ID if it wasn't set
            $order->set_customer_id($current_user_id);
            $order->save();
            $this->log("Updated order {$order->get_id()} with current user ID: {$current_user_id}");
            return $current_user_id;
        }

        // Check if user is logged in via WooCommerce session
        if (WC()->customer && WC()->customer->get_id() > 0) {
            $wc_customer_id = WC()->customer->get_id();
            // Update the order with the WooCommerce customer ID
            $order->set_customer_id($wc_customer_id);
            $order->save();
            $this->log("Updated order {$order->get_id()} with WC customer ID: {$wc_customer_id}");
            return $wc_customer_id;
        }

        // Try to find existing customer by email for authenticated context
        $billing_email = $order->get_billing_email();
        if (!empty($billing_email) && is_user_logged_in()) {
            $user = get_user_by('email', $billing_email);
            if ($user && $user->ID > 0) {
                // Update the order with the found user ID
                $order->set_customer_id($user->ID);
                $order->save();
                $this->log("Found and assigned user ID {$user->ID} for order {$order->get_id()} based on email {$billing_email}");
                return $user->ID;
            }
        }

        // Handle guest users differently based on usage
        if ($for_mandate_storage) {
            // For PayTo mandates, we don't store for guest users
            // Return 0 to indicate this is a guest user
            $this->log("Guest user detected for order {$order->get_id()} - no mandate storage");
            return 0;
        } else {
            // For API usage, generate a guest customer ID
            // This ensures consistency across payment attempts for the same order
            $guest_customer_id = 'guest_' . $order->get_id() . '_' . time();
            $this->log("Using guest customer ID: {$guest_customer_id} for order {$order->get_id()}");
            return $guest_customer_id;
        }
    }

    /**
     * Clean up scheduled events on plugin deactivation
     */
    public static function cleanup_scheduled_events() {
        wp_clear_scheduled_hook('monoova_check_payto_agreements');
    }
}
